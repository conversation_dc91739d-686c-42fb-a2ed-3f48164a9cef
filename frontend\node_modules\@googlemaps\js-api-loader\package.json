{"name": "@googlemaps/js-api-loader", "type": "commonjs", "version": "1.16.10", "description": "Wrapper for the loading of Google Maps JavaScript API script in the browser", "keywords": ["google", "maps"], "homepage": "https://github.com/googlemaps/js-api-loader", "bugs": {"url": "https://github.com/googlemaps/js-api-loader/issues"}, "repository": {"type": "git", "url": "https://github.com/googlemaps/js-api-loader.git"}, "license": "Apache-2.0", "author": "<PERSON>", "main": "dist/index.umd.js", "unpkg": "dist/index.min.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/", "src/"], "scripts": {"docs": "typedoc src/index.ts && cp -r dist docs/dist && cp -r examples docs/examples", "format": "eslint . --fix", "lint": "eslint .", "prepare": "rm -rf dist && rollup -c", "test": "jest src/*", "test:e2e": "jest e2e/*"}, "devDependencies": {"@babel/preset-env": "^7.9.5", "@babel/runtime-corejs3": "^7.9.2", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^12.1.0", "@types/google.maps": "^3.53.1", "@types/jest": "^30.0.0", "@types/selenium-webdriver": "^4.0.9", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "core-js": "^3.6.4", "eslint": "^8.57.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.0.0", "fast-deep-equal": "^3.1.3", "geckodriver": "^5.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "prettier": "^3.0.3", "rollup": "^4.6.1", "selenium-webdriver": "^4.0.0-alpha.7", "ts-jest": "^29.1.1", "tslib": "^2.8.1", "typedoc": "^0.28.5", "typescript": "^5.5.4"}, "publishConfig": {"access": "public", "registry": "https://wombat-dressing-room.appspot.com"}, "prettier": {"trailingComma": "es5"}}
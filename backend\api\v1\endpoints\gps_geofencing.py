"""
RT46 Fleet Management System - GPS & Geofencing API Endpoints
Government Fleet Management for South African Institutions
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Path, Body
from fastapi.responses import JSONResponse
import structlog

from backend.models.gps_geofencing import (
    Geofence, GeofenceViolation, GeofenceType, GeofenceViolationType,
    GeofenceEvaluationRequest, GeofenceEvaluationResponse, Location,
    GeofenceStatistics, GeofenceHealth
)
from backend.services.gps_geofencing import (
    GPSGeofencingService, GeofenceEvaluationEngine, RealTimeViolationChecker
)

logger = structlog.get_logger()

router = APIRouter(tags=["GPS & Geofencing Management"])

# Initialize services
gps_service = GPSGeofencingService()
evaluation_engine = GeofenceEvaluationEngine()
realtime_checker = RealTimeViolationChecker()


@router.get("/health", response_model=GeofenceHealth)
async def health_check():
    """Health check endpoint for GPS & Geofencing service"""
    try:
        health = gps_service.get_health()
        
        logger.info(
            "GPS & Geofencing health check completed",
            status=health.status,
            geofences_loaded=health.geofences_loaded
        )
        
        return health
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=500, detail="Service unhealthy")


@router.post("/geofences", response_model=Geofence)
async def create_geofence(geofence_data: Dict[str, Any]):
    """Create a new geofence"""
    try:
        geofence = gps_service.create_geofence(geofence_data)
        
        # Add to evaluation engine and real-time checker
        evaluation_engine.add_geofence(geofence)
        realtime_checker.add_geofence(geofence)
        
        logger.info(
            "Geofence created successfully",
            geofence_id=geofence.id,
            name=geofence.name,
            geofence_type=geofence.geofence_type
        )
        
        return geofence
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Error creating geofence", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/geofences", response_model=List[Geofence])
async def get_geofences(
    active_only: bool = Query(True, description="Return only active geofences"),
    geofence_type: Optional[GeofenceType] = Query(None, description="Filter by geofence type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of geofences to return")
):
    """Get all geofences with optional filtering"""
    try:
        geofences = gps_service.get_all_geofences(active_only=active_only)
        
        # Apply type filter if specified
        if geofence_type:
            geofences = [g for g in geofences if g.geofence_type == geofence_type]
        
        # Apply limit
        geofences = geofences[:limit]
        
        logger.info(
            "Geofences retrieved successfully",
            count=len(geofences),
            active_only=active_only,
            geofence_type=geofence_type.value if geofence_type else None
        )
        
        return geofences
        
    except Exception as e:
        logger.error("Error retrieving geofences", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/geofences/{geofence_id}", response_model=Geofence)
async def get_geofence(geofence_id: str = Path(..., description="Geofence ID")):
    """Get a specific geofence by ID"""
    try:
        geofence = gps_service.get_geofence(geofence_id)
        
        if not geofence:
            raise HTTPException(status_code=404, detail="Geofence not found")
        
        logger.info(
            "Geofence retrieved successfully",
            geofence_id=geofence_id
        )
        
        return geofence
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error retrieving geofence", error=str(e), geofence_id=geofence_id)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/geofences/{geofence_id}", response_model=Geofence)
async def update_geofence(
    geofence_id: str = Path(..., description="Geofence ID"),
    update_data: Dict[str, Any] = Body(..., description="Update data")
):
    """Update an existing geofence"""
    try:
        geofence = gps_service.update_geofence(geofence_id, update_data)
        
        # Update in evaluation engine and real-time checker
        evaluation_engine.remove_geofence(geofence_id)
        evaluation_engine.add_geofence(geofence)
        
        realtime_checker.evaluation_engine.remove_geofence(geofence_id)
        realtime_checker.evaluation_engine.add_geofence(geofence)
        
        logger.info(
            "Geofence updated successfully",
            geofence_id=geofence_id,
            updated_fields=list(update_data.keys())
        )
        
        return geofence
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Error updating geofence", error=str(e), geofence_id=geofence_id)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/geofences/{geofence_id}")
async def delete_geofence(geofence_id: str = Path(..., description="Geofence ID")):
    """Delete a geofence"""
    try:
        result = gps_service.delete_geofence(geofence_id)
        
        # Remove from evaluation engine and real-time checker
        evaluation_engine.remove_geofence(geofence_id)
        realtime_checker.evaluation_engine.remove_geofence(geofence_id)
        
        logger.info(
            "Geofence deleted successfully",
            geofence_id=geofence_id
        )
        
        return {"message": "Geofence deleted successfully", "geofence_id": geofence_id}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Error deleting geofence", error=str(e), geofence_id=geofence_id)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/evaluate", response_model=GeofenceEvaluationResponse)
async def evaluate_location(request: GeofenceEvaluationRequest):
    """Evaluate a location against all geofences"""
    try:
        response = evaluation_engine.evaluate_location(request)
        
        logger.info(
            "Location evaluation completed",
            vehicle_id=request.vehicle_id,
            violations_count=len(response.violations),
            evaluation_time_ms=response.evaluation_time_ms
        )
        
        return response
        
    except Exception as e:
        logger.error("Error evaluating location", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/evaluate/telemetry")
async def evaluate_telemetry_payload(telemetry_payload: Dict[str, Any]):
    """Evaluate a telemetry payload for violations"""
    try:
        violations = realtime_checker.check_telemetry_payload(telemetry_payload)
        
        logger.info(
            "Telemetry payload evaluated",
            vehicle_id=telemetry_payload.get("vehicle_id"),
            violations_count=len(violations)
        )
        
        return {
            "violations": violations,
            "violation_count": len(violations),
            "vehicle_id": telemetry_payload.get("vehicle_id")
        }
        
    except Exception as e:
        logger.error("Error evaluating telemetry payload", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/evaluate/telemetry/batch")
async def evaluate_batch_telemetry_payloads(telemetry_payloads: List[Dict[str, Any]]):
    """Evaluate multiple telemetry payloads for violations"""
    try:
        violations = realtime_checker.check_batch_telemetry_payloads(telemetry_payloads)
        
        logger.info(
            "Batch telemetry payloads evaluated",
            payload_count=len(telemetry_payloads),
            violations_count=len(violations)
        )
        
        return {
            "violations": violations,
            "violation_count": len(violations),
            "payload_count": len(telemetry_payloads)
        }
        
    except Exception as e:
        logger.error("Error evaluating batch telemetry payloads", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/violations", response_model=List[GeofenceViolation])
async def get_violations(
    geofence_id: Optional[str] = Query(None, description="Filter by geofence ID"),
    vehicle_id: Optional[str] = Query(None, description="Filter by vehicle ID"),
    violation_type: Optional[GeofenceViolationType] = Query(None, description="Filter by violation type"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    start_date: Optional[datetime] = Query(None, description="Start date for filtering"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of violations to return")
):
    """Get violations with optional filtering"""
    try:
        violations = gps_service.get_violations(
            geofence_id=geofence_id,
            vehicle_id=vehicle_id,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        # Apply additional filters
        if violation_type:
            violations = [v for v in violations if v.violation_type == violation_type]
        
        if severity:
            violations = [v for v in violations if v.severity == severity]
        
        logger.info(
            "Violations retrieved successfully",
            count=len(violations),
            geofence_id=geofence_id,
            vehicle_id=vehicle_id,
            violation_type=violation_type.value if violation_type else None
        )
        
        return violations
        
    except Exception as e:
        logger.error("Error retrieving violations", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/violations/recent")
async def get_recent_violations(
    minutes: int = Query(60, ge=1, le=1440, description="Minutes to look back")
):
    """Get recent violations from the real-time checker"""
    try:
        violations = realtime_checker.get_recent_violations(minutes=minutes)
        
        logger.info(
            "Recent violations retrieved",
            count=len(violations),
            minutes=minutes
        )
        
        return {
            "violations": violations,
            "count": len(violations),
            "minutes": minutes
        }
        
    except Exception as e:
        logger.error("Error retrieving recent violations", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/violations/{violation_id}", response_model=GeofenceViolation)
async def get_violation(violation_id: str = Path(..., description="Violation ID")):
    """Get a specific violation by ID"""
    try:
        # This would typically query a database
        # For now, search in memory
        violations = gps_service.get_violations()
        violation = next((v for v in violations if v.id == violation_id), None)
        
        if not violation:
            raise HTTPException(status_code=404, detail="Violation not found")
        
        logger.info(
            "Violation retrieved successfully",
            violation_id=violation_id
        )
        
        return violation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error retrieving violation", error=str(e), violation_id=violation_id)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/violations/{violation_id}/acknowledge")
async def acknowledge_violation(
    violation_id: str = Path(..., description="Violation ID"),
    acknowledged_by: str = Body(..., description="User who acknowledged the violation")
):
    """Acknowledge a violation"""
    try:
        # This would typically update a database
        # For now, just return success
        logger.info(
            "Violation acknowledged",
            violation_id=violation_id,
            acknowledged_by=acknowledged_by
        )
        
        return {
            "message": "Violation acknowledged successfully",
            "violation_id": violation_id,
            "acknowledged_by": acknowledged_by,
            "acknowledged_at": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error("Error acknowledging violation", error=str(e), violation_id=violation_id)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/search/nearby")
async def search_geofences_nearby(
    latitude: float = Query(..., ge=-90, le=90, description="Latitude"),
    longitude: float = Query(..., ge=-180, le=180, description="Longitude"),
    radius_km: float = Query(10.0, ge=0.1, le=100.0, description="Search radius in kilometers")
):
    """Search for geofences near a location"""
    try:
        location = Location(latitude=latitude, longitude=longitude)
        nearby_geofences = gps_service.search_geofences_by_location(location, radius_km)
        
        logger.info(
            "Nearby geofences search completed",
            latitude=latitude,
            longitude=longitude,
            radius_km=radius_km,
            found_count=len(nearby_geofences)
        )
        
        return {
            "location": location,
            "radius_km": radius_km,
            "geofences": nearby_geofences,
            "count": len(nearby_geofences)
        }
        
    except Exception as e:
        logger.error("Error searching nearby geofences", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=GeofenceStatistics)
async def get_statistics():
    """Get geofence and violation statistics"""
    try:
        statistics = gps_service.get_statistics()
        
        logger.info(
            "Statistics retrieved successfully",
            total_geofences=statistics.total_geofences,
            total_violations=statistics.total_violations
        )
        
        return statistics
        
    except Exception as e:
        logger.error("Error retrieving statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics/realtime")
async def get_realtime_statistics():
    """Get real-time violation statistics"""
    try:
        stats = realtime_checker.get_violation_stats()
        
        logger.info(
            "Real-time statistics retrieved",
            total_violations=stats["total_violations"]
        )
        
        return stats
        
    except Exception as e:
        logger.error("Error retrieving real-time statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/geofences/import")
async def import_geofences(
    geofences_data: List[Dict[str, Any]] = Body(..., description="List of geofence data")
):
    """Import multiple geofences"""
    try:
        imported_geofences = []
        
        for geofence_data in geofences_data:
            geofence = gps_service.create_geofence(geofence_data)
            evaluation_engine.add_geofence(geofence)
            realtime_checker.add_geofence(geofence)
            imported_geofences.append(geofence)
        
        logger.info(
            "Geofences imported successfully",
            count=len(imported_geofences)
        )
        
        return {
            "message": f"Successfully imported {len(imported_geofences)} geofences",
            "imported_count": len(imported_geofences),
            "geofences": imported_geofences
        }
        
    except Exception as e:
        logger.error("Error importing geofences", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/geofences/export")
async def export_geofences(format: str = Query("json", description="Export format")):
    """Export all geofences"""
    try:
        geofences = gps_service.get_all_geofences(active_only=False)
        
        if format.lower() == "json":
            import json
            geofences_data = [geofence.dict() for geofence in geofences]
            return JSONResponse(
                content=json.dumps(geofences_data, indent=2, default=str),
                media_type="application/json"
            )
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported format: {format}")
        
    except Exception as e:
        logger.error("Error exporting geofences", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error") 
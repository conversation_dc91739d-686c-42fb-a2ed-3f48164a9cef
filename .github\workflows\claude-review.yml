name: Claude <PERSON> Review

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - 'frontend/**'
      - 'scripts/**'
      - 'tests/**'

jobs:
  claude-review:
    name: Claude Code Review
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        
    - name: Install dependencies
      run: |
        pip install anthropic requests
        
    - name: Run Claude Code Review
      env:
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
      run: |
        python -c "
        import os
        import json
        import subprocess
        from anthropic import Anthropic
        
        # Get changed files
        result = subprocess.run(['git', 'diff', '--name-only', '${{ github.event.pull_request.base.sha }}', '${{ github.event.pull_request.head.sha }}'], 
                              capture_output=True, text=True)
        changed_files = result.stdout.strip().split('\n') if result.stdout else []
        
        # Filter relevant files
        relevant_files = [f for f in changed_files if f and any(f.endswith(ext) for ext in ['.py', '.js', '.ts', '.jsx', '.tsx', '.json', '.md', '.yml', '.yaml'])]
        
        if not relevant_files:
            print('No relevant files to review')
            exit(0)
        
        print(f'Reviewing {len(relevant_files)} files: {relevant_files}')
        
        # Initialize Claude client
        client = Anthropic(api_key=os.environ['ANTHROPIC_API_KEY'])
        
        review_results = {
            'summary': '',
            'issues': [],
            'recommendations': [],
            'usage': {'input_tokens': 0, 'output_tokens': 0}
        }
        
        try:
            # Read file contents (limit to 3 files to avoid usage limits)
            file_contents = {}
            for file_path in relevant_files[:3]:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Truncate if too long
                        if len(content) > 2000:
                            content = content[:2000] + '... [truncated]'
                        file_contents[file_path] = content
                except Exception as e:
                    print(f'Error reading {file_path}: {e}')
            
            if not file_contents:
                print('No files could be read')
                exit(0)
            
            # Create review prompt
            files_text = '\n\n'.join([f'File: {path}\n```\n{content}\n```' for path, content in file_contents.items()])
            
            prompt = f'''Please review the following code changes for the RT46 Fleet Management System (South African government project).

Focus on:
1. Security issues (especially for government systems)
2. Code quality and best practices
3. Performance considerations
4. South African compliance requirements

Code changes:
{files_text}

Please provide a brief summary and any critical issues found. Keep it concise.'''

            # Make API call with strict limits
            response = client.messages.create(
                model='claude-3-sonnet-20240229',
                max_tokens=1000,  # Strict limit
                temperature=0.1,
                messages=[{'role': 'user', 'content': prompt}]
            )
            
            # Parse response
            review_text = response.content[0].text
            review_results['summary'] = review_text
            review_results['usage'] = {
                'input_tokens': response.usage.input_tokens,
                'output_tokens': response.usage.output_tokens
            }
            
            print('Review completed successfully')
            
        except Exception as e:
            print(f'Error during review: {e}')
            review_results['summary'] = f'Review failed: {str(e)}'
        
        # Save results
        with open('claude-review-results.json', 'w') as f:
            json.dump(review_results, f, indent=2)
        "
        
    - name: Comment Review Results
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          try {
            const reviewFile = 'claude-review-results.json';
            if (fs.existsSync(reviewFile)) {
              const reviewData = JSON.parse(fs.readFileSync(reviewFile, 'utf8'));
              
              let comment = '## 🤖 Claude Code Review\n\n';
              
              if (reviewData.summary) {
                comment += reviewData.summary + '\n\n';
              }
              
              if (reviewData.usage) {
                comment += `---\n*Review completed using ${reviewData.usage.input_tokens} input tokens and ${reviewData.usage.output_tokens} output tokens*`;
              }
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } else {
              const fallbackComment = `## 🤖 Claude Code Review\n\n⚠️ Review completed but no results file found.\n\nPlease check the workflow logs for details.`;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: fallbackComment
              });
            }
          } catch (error) {
            console.error('Error creating review comment:', error);
            
            const errorComment = `## 🤖 Claude Code Review\n\n❌ Review failed.\n\n**Error**: ${error.message}\n\nPlease check the workflow logs for details.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: errorComment
            });
          } 